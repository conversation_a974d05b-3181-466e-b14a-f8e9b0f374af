import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const uploadId = searchParams.get('uploadId');

    if (!uploadId) {
      return NextResponse.json(
        { error: 'Upload ID is required' },
        { status: 400 }
      );
    }

    // Get upload status
    const { data: upload, error: uploadError } = await supabase
      .from('csv_uploads')
      .select('*')
      .eq('id', uploadId)
      .eq('user_id', user.id)
      .single();

    if (uploadError || !upload) {
      return NextResponse.json(
        { error: 'Upload not found' },
        { status: 404 }
      );
    }

    // Calculate progress percentage
    let progressPercentage = 0;
    if (upload.total_rows > 0) {
      progressPercentage = Math.round((upload.processed_rows / upload.total_rows) * 100);
    }

    // Get error count if there are errors
    let errorDetails = null;
    if (upload.error_rows > 0) {
      const { data: errors } = await supabase
        .from('csv_upload_errors')
        .select('error_type, error_message, row_number')
        .eq('upload_id', uploadId)
        .order('row_number')
        .limit(10); // Limit to first 10 errors for preview

      errorDetails = {
        totalErrors: upload.error_rows,
        sampleErrors: errors || []
      };
    }

    // Calculate processing time
    let processingTime = null;
    if (upload.processing_started_at) {
      const startTime = new Date(upload.processing_started_at);
      const endTime = upload.processing_completed_at 
        ? new Date(upload.processing_completed_at)
        : new Date();
      processingTime = Math.round((endTime.getTime() - startTime.getTime()) / 1000); // seconds
    }

    const response = {
      uploadId: upload.id,
      filename: upload.original_filename,
      status: upload.status,
      platform: upload.platform,
      uploadDate: upload.upload_date,
      processingStartedAt: upload.processing_started_at,
      processingCompletedAt: upload.processing_completed_at,
      processingTime,
      progress: {
        totalRows: upload.total_rows,
        processedRows: upload.processed_rows,
        successfulRows: upload.successful_rows,
        errorRows: upload.error_rows,
        duplicateRows: upload.duplicate_rows,
        percentage: progressPercentage
      },
      errorMessage: upload.error_message,
      errorDetails,
      processingLog: upload.processing_log
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Upload status API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST endpoint to update upload status (for internal use)
export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { uploadId, status, progress, errorMessage, processingLog } = body;

    if (!uploadId) {
      return NextResponse.json(
        { error: 'Upload ID is required' },
        { status: 400 }
      );
    }

    // Prepare update data
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (status) {
      updateData.status = status;
      
      if (status === 'processing' && !progress?.processingStartedAt) {
        updateData.processing_started_at = new Date().toISOString();
      }
      
      if (['completed', 'failed', 'cancelled'].includes(status)) {
        updateData.processing_completed_at = new Date().toISOString();
      }
    }

    if (progress) {
      if (progress.totalRows !== undefined) updateData.total_rows = progress.totalRows;
      if (progress.processedRows !== undefined) updateData.processed_rows = progress.processedRows;
      if (progress.successfulRows !== undefined) updateData.successful_rows = progress.successfulRows;
      if (progress.errorRows !== undefined) updateData.error_rows = progress.errorRows;
      if (progress.duplicateRows !== undefined) updateData.duplicate_rows = progress.duplicateRows;
    }

    if (errorMessage) {
      updateData.error_message = errorMessage;
    }

    if (processingLog) {
      updateData.processing_log = processingLog;
    }

    // Update upload record
    const { data, error } = await supabase
      .from('csv_uploads')
      .update(updateData)
      .eq('id', uploadId)
      .eq('user_id', user.id)
      .select()
      .single();

    if (error) {
      console.error('Error updating upload status:', error);
      return NextResponse.json(
        { error: 'Failed to update upload status' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true, data });

  } catch (error) {
    console.error('Upload status update API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
