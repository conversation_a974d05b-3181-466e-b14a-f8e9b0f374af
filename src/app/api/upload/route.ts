import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/supabase/client/server';
import { CsvProcessor, validateCsvFile } from '@/lib/csv-processor';

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_TYPES = ['text/csv', 'application/vnd.ms-excel'];

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const platform = formData.get('platform') as string || 'shopmy';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: `File size exceeds ${MAX_FILE_SIZE / 1024 / 1024}MB limit` },
        { status: 400 }
      );
    }

    if (!ALLOWED_TYPES.includes(file.type) && !file.name.toLowerCase().endsWith('.csv')) {
      return NextResponse.json(
        { error: 'Invalid file type. Only CSV files are allowed.' },
        { status: 400 }
      );
    }

    // Read file content
    const fileContent = await file.text();

    // Validate CSV format
    const validation = await validateCsvFile(fileContent);
    if (!validation.isValid) {
      return NextResponse.json(
        {
          error: 'Invalid CSV format',
          details: validation.errors
        },
        { status: 400 }
      );
    }

    // Create upload record
    const { data: uploadRecord, error: uploadError } = await supabase
      .from('csv_uploads')
      .insert({
        user_id: user.id,
        filename: `${Date.now()}-${file.name}`,
        original_filename: file.name,
        file_size: file.size,
        platform: platform,
        status: 'uploaded',
        total_rows: validation.rowCount
      })
      .select()
      .single();

    if (uploadError) {
      console.error('Error creating upload record:', uploadError);
      return NextResponse.json(
        { error: 'Failed to create upload record' },
        { status: 500 }
      );
    }

    // Start processing in background
    const processor = new CsvProcessor(
      uploadRecord.id,
      uploadRecord.filename,
      user.id,
      supabase,
      {
        batchSize: 100,
        skipDuplicates: true
      }
    );

    // Process file asynchronously
    processor.processFile(fileContent).catch(error => {
      console.error('Background processing error:', error);
    });

    return NextResponse.json({
      success: true,
      uploadId: uploadRecord.id,
      filename: uploadRecord.original_filename,
      totalRows: validation.rowCount,
      status: 'processing'
    });

  } catch (error) {
    console.error('Upload API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const platform = searchParams.get('platform');

    // Calculate offset
    const offset = (page - 1) * limit;

    // Build query
    let query = supabase
      .from('csv_uploads')
      .select('*', { count: 'exact' })
      .eq('user_id', user.id)
      .order('upload_date', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }

    if (platform) {
      query = query.eq('platform', platform);
    }

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching uploads:', error);
      return NextResponse.json(
        { error: 'Failed to fetch uploads' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      data: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error) {
    console.error('Upload history API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
