import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const uploadId = searchParams.get('uploadId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const errorType = searchParams.get('errorType');

    if (!uploadId) {
      return NextResponse.json(
        { error: 'Upload ID is required' },
        { status: 400 }
      );
    }

    // Verify upload belongs to user
    const { data: upload, error: uploadError } = await supabase
      .from('csv_uploads')
      .select('id, original_filename, error_rows')
      .eq('id', uploadId)
      .eq('user_id', user.id)
      .single();

    if (uploadError || !upload) {
      return NextResponse.json(
        { error: 'Upload not found' },
        { status: 404 }
      );
    }

    // Calculate offset
    const offset = (page - 1) * limit;

    // Build query for errors
    let query = supabase
      .from('csv_upload_errors')
      .select('*', { count: 'exact' })
      .eq('upload_id', uploadId)
      .order('row_number')
      .range(offset, offset + limit - 1);

    // Apply error type filter
    if (errorType) {
      query = query.eq('error_type', errorType);
    }

    const { data: errors, error: errorsError, count } = await query;

    if (errorsError) {
      console.error('Error fetching upload errors:', errorsError);
      return NextResponse.json(
        { error: 'Failed to fetch errors' },
        { status: 500 }
      );
    }

    // Get error type summary
    const { data: errorSummary } = await supabase
      .from('csv_upload_errors')
      .select('error_type')
      .eq('upload_id', uploadId);

    const errorTypeCounts = errorSummary?.reduce((acc: any, error: any) => {
      acc[error.error_type] = (acc[error.error_type] || 0) + 1;
      return acc;
    }, {}) || {};

    return NextResponse.json({
      uploadId,
      filename: upload.original_filename,
      totalErrors: upload.error_rows,
      errors: errors || [],
      errorTypeCounts,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error) {
    console.error('Upload errors API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST endpoint to log errors (for internal use during processing)
export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { uploadId, errors } = body;

    if (!uploadId || !Array.isArray(errors)) {
      return NextResponse.json(
        { error: 'Upload ID and errors array are required' },
        { status: 400 }
      );
    }

    // Verify upload belongs to user
    const { data: upload, error: uploadError } = await supabase
      .from('csv_uploads')
      .select('id')
      .eq('id', uploadId)
      .eq('user_id', user.id)
      .single();

    if (uploadError || !upload) {
      return NextResponse.json(
        { error: 'Upload not found' },
        { status: 404 }
      );
    }

    // Prepare error records
    const errorRecords = errors.map((error: any) => ({
      upload_id: uploadId,
      row_number: error.rowNumber,
      error_type: error.errorType,
      error_message: error.errorMessage,
      field_name: error.fieldName || null,
      field_value: error.fieldValue || null,
      raw_row_data: error.rawRowData || null
    }));

    // Insert errors
    const { error: insertError } = await supabase
      .from('csv_upload_errors')
      .insert(errorRecords);

    if (insertError) {
      console.error('Error inserting upload errors:', insertError);
      return NextResponse.json(
        { error: 'Failed to log errors' },
        { status: 500 }
      );
    }

    return NextResponse.json({ 
      success: true, 
      errorsLogged: errorRecords.length 
    });

  } catch (error) {
    console.error('Upload errors logging API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
