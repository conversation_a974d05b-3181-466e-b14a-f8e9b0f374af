/**
 * Upload Rollback System
 * 
 * Provides functionality to undo CSV uploads and remove associated
 * transactions from the database safely.
 */

import { createClient } from '@supabase/supabase-js';

export interface RollbackResult {
  success: boolean;
  deletedTransactions: number;
  deletedErrors: number;
  message: string;
  errors?: string[];
}

export interface RollbackOptions {
  dryRun?: boolean; // If true, only simulate the rollback
  batchSize?: number; // Number of records to delete per batch
  confirmationRequired?: boolean; // Require explicit confirmation
}

/**
 * Upload rollback manager
 */
export class UploadRollbackManager {
  private supabase: any;
  private uploadId: string;
  private userId: string;

  constructor(uploadId: string, userId: string, supabaseClient: any) {
    this.supabase = supabaseClient;
    this.uploadId = uploadId;
    this.userId = userId;
  }

  /**
   * Perform rollback of an upload
   */
  async rollback(options: RollbackOptions = {}): Promise<RollbackResult> {
    const {
      dryRun = false,
      batchSize = 100,
      confirmationRequired = true
    } = options;

    try {
      // Verify upload belongs to user
      const uploadVerification = await this.verifyUploadOwnership();
      if (!uploadVerification.success) {
        return {
          success: false,
          deletedTransactions: 0,
          deletedErrors: 0,
          message: uploadVerification.message
        };
      }

      // Get upload details
      const uploadDetails = await this.getUploadDetails();
      if (!uploadDetails) {
        return {
          success: false,
          deletedTransactions: 0,
          deletedErrors: 0,
          message: 'Upload not found'
        };
      }

      // Check if upload can be rolled back
      const canRollback = await this.canRollback(uploadDetails);
      if (!canRollback.allowed) {
        return {
          success: false,
          deletedTransactions: 0,
          deletedErrors: 0,
          message: canRollback.reason
        };
      }

      // Count transactions to be deleted
      const transactionCount = await this.countTransactionsToDelete();
      const errorCount = await this.countErrorsToDelete();

      if (dryRun) {
        return {
          success: true,
          deletedTransactions: transactionCount,
          deletedErrors: errorCount,
          message: `Dry run: Would delete ${transactionCount} transactions and ${errorCount} error records`
        };
      }

      // Perform the actual rollback
      const result = await this.performRollback(batchSize);
      
      // Update upload status
      await this.updateUploadStatus('rolled_back');

      return {
        success: true,
        deletedTransactions: result.deletedTransactions,
        deletedErrors: result.deletedErrors,
        message: `Successfully rolled back upload. Deleted ${result.deletedTransactions} transactions and ${result.deletedErrors} error records.`
      };

    } catch (error) {
      console.error('Rollback error:', error);
      return {
        success: false,
        deletedTransactions: 0,
        deletedErrors: 0,
        message: 'Rollback failed due to an unexpected error',
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }

  /**
   * Verify that the upload belongs to the current user
   */
  private async verifyUploadOwnership(): Promise<{ success: boolean; message: string }> {
    try {
      const { data, error } = await this.supabase
        .from('csv_uploads')
        .select('user_id')
        .eq('id', this.uploadId)
        .single();

      if (error) {
        return { success: false, message: 'Upload not found' };
      }

      if (data.user_id !== this.userId) {
        return { success: false, message: 'Unauthorized: Upload does not belong to current user' };
      }

      return { success: true, message: 'Ownership verified' };
    } catch (error) {
      return { success: false, message: 'Failed to verify upload ownership' };
    }
  }

  /**
   * Get upload details
   */
  private async getUploadDetails() {
    try {
      const { data, error } = await this.supabase
        .from('csv_uploads')
        .select('*')
        .eq('id', this.uploadId)
        .single();

      return error ? null : data;
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if upload can be rolled back
   */
  private async canRollback(uploadDetails: any): Promise<{ allowed: boolean; reason: string }> {
    // Check upload status
    if (uploadDetails.status === 'processing') {
      return {
        allowed: false,
        reason: 'Cannot rollback upload that is currently being processed'
      };
    }

    if (uploadDetails.status === 'rolled_back') {
      return {
        allowed: false,
        reason: 'Upload has already been rolled back'
      };
    }

    if (uploadDetails.status === 'failed') {
      // Allow rollback of failed uploads to clean up partial data
      return { allowed: true, reason: 'Rollback allowed for failed upload' };
    }

    if (uploadDetails.status === 'completed') {
      // Check if upload is recent (within 30 days)
      const uploadDate = new Date(uploadDetails.upload_date);
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      if (uploadDate < thirtyDaysAgo) {
        return {
          allowed: false,
          reason: 'Cannot rollback uploads older than 30 days'
        };
      }

      return { allowed: true, reason: 'Rollback allowed for recent completed upload' };
    }

    return { allowed: true, reason: 'Rollback allowed' };
  }

  /**
   * Count transactions that would be deleted
   */
  private async countTransactionsToDelete(): Promise<number> {
    try {
      const { count, error } = await this.supabase
        .from('normalized_transactions')
        .select('*', { count: 'exact', head: true })
        .eq('upload_batch_id', this.uploadId);

      return error ? 0 : (count || 0);
    } catch (error) {
      return 0;
    }
  }

  /**
   * Count error records that would be deleted
   */
  private async countErrorsToDelete(): Promise<number> {
    try {
      const { count, error } = await this.supabase
        .from('csv_upload_errors')
        .select('*', { count: 'exact', head: true })
        .eq('upload_id', this.uploadId);

      return error ? 0 : (count || 0);
    } catch (error) {
      return 0;
    }
  }

  /**
   * Perform the actual rollback operation
   */
  private async performRollback(batchSize: number): Promise<{
    deletedTransactions: number;
    deletedErrors: number;
  }> {
    let deletedTransactions = 0;
    let deletedErrors = 0;

    // Delete transactions in batches
    while (true) {
      const { data: transactions, error: fetchError } = await this.supabase
        .from('normalized_transactions')
        .select('id')
        .eq('upload_batch_id', this.uploadId)
        .limit(batchSize);

      if (fetchError || !transactions || transactions.length === 0) {
        break;
      }

      const transactionIds = transactions.map(t => t.id);
      const { error: deleteError } = await this.supabase
        .from('normalized_transactions')
        .delete()
        .in('id', transactionIds);

      if (deleteError) {
        throw new Error(`Failed to delete transactions: ${deleteError.message}`);
      }

      deletedTransactions += transactions.length;

      // If we got fewer than batchSize, we're done
      if (transactions.length < batchSize) {
        break;
      }
    }

    // Delete error records in batches
    while (true) {
      const { data: errors, error: fetchError } = await this.supabase
        .from('csv_upload_errors')
        .select('id')
        .eq('upload_id', this.uploadId)
        .limit(batchSize);

      if (fetchError || !errors || errors.length === 0) {
        break;
      }

      const errorIds = errors.map(e => e.id);
      const { error: deleteError } = await this.supabase
        .from('csv_upload_errors')
        .delete()
        .in('id', errorIds);

      if (deleteError) {
        throw new Error(`Failed to delete error records: ${deleteError.message}`);
      }

      deletedErrors += errors.length;

      // If we got fewer than batchSize, we're done
      if (errors.length < batchSize) {
        break;
      }
    }

    return { deletedTransactions, deletedErrors };
  }

  /**
   * Update upload status after rollback
   */
  private async updateUploadStatus(status: string) {
    try {
      await this.supabase
        .from('csv_uploads')
        .update({
          status,
          updated_at: new Date().toISOString(),
          processing_log: {
            rolled_back_at: new Date().toISOString(),
            rolled_back_by: this.userId
          }
        })
        .eq('id', this.uploadId);
    } catch (error) {
      console.error('Failed to update upload status after rollback:', error);
    }
  }

  /**
   * Get rollback preview information
   */
  async getRollbackPreview(): Promise<{
    canRollback: boolean;
    reason: string;
    transactionCount: number;
    errorCount: number;
    uploadDetails: any;
    estimatedTime: string;
  }> {
    const uploadDetails = await this.getUploadDetails();
    if (!uploadDetails) {
      return {
        canRollback: false,
        reason: 'Upload not found',
        transactionCount: 0,
        errorCount: 0,
        uploadDetails: null,
        estimatedTime: '0 seconds'
      };
    }

    const canRollback = await this.canRollback(uploadDetails);
    const transactionCount = await this.countTransactionsToDelete();
    const errorCount = await this.countErrorsToDelete();

    // Estimate time based on record count (rough estimate: 100 records per second)
    const totalRecords = transactionCount + errorCount;
    const estimatedSeconds = Math.max(1, Math.ceil(totalRecords / 100));
    const estimatedTime = estimatedSeconds < 60 
      ? `${estimatedSeconds} seconds`
      : `${Math.ceil(estimatedSeconds / 60)} minutes`;

    return {
      canRollback: canRollback.allowed,
      reason: canRollback.reason,
      transactionCount,
      errorCount,
      uploadDetails,
      estimatedTime
    };
  }
}

/**
 * Utility function to create rollback manager
 */
export function createRollbackManager(
  uploadId: string, 
  userId: string, 
  supabaseClient: any
): UploadRollbackManager {
  return new UploadRollbackManager(uploadId, userId, supabaseClient);
}
