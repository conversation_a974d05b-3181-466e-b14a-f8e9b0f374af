/**
 * Client-side CSV processor that handles chunked processing
 * This runs in the browser and makes API calls to process data in batches
 */

export interface ProcessingProgress {
  uploadId: string;
  processedRows: number;
  totalRows: number;
  successfulRows: number;
  errorRows: number;
  isComplete: boolean;
  progress: number;
  errors?: any[];
}

export class ClientCsvProcessor {
  private uploadId: string;
  private onProgress?: (progress: ProcessingProgress) => void;

  constructor(uploadId: string, onProgress?: (progress: ProcessingProgress) => void) {
    this.uploadId = uploadId;
    this.onProgress = onProgress;
  }

  /**
   * Start processing the uploaded CSV file
   */
  async startProcessing(): Promise<ProcessingProgress> {
    let startRow = 0;
    let isComplete = false;
    let lastProgress: ProcessingProgress = {
      uploadId: this.uploadId,
      processedRows: 0,
      totalRows: 0,
      successfulRows: 0,
      errorRows: 0,
      isComplete: false,
      progress: 0
    };

    while (!isComplete) {
      try {
        const response = await fetch('/api/upload/process', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            uploadId: this.uploadId,
            startRow
          })
        });

        if (!response.ok) {
          throw new Error(`Processing failed: ${response.statusText}`);
        }

        const result = await response.json();
        
        lastProgress = {
          uploadId: this.uploadId,
          processedRows: result.processedRows,
          totalRows: result.totalRows,
          successfulRows: result.processedRows, // Simplified for demo
          errorRows: 0,
          isComplete: result.isComplete,
          progress: result.progress
        };

        // Report progress
        if (this.onProgress) {
          this.onProgress(lastProgress);
        }

        isComplete = result.isComplete;
        startRow = result.nextStartRow || 0;

        // Add a small delay to prevent overwhelming the server
        if (!isComplete) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }

      } catch (error) {
        console.error('Processing batch failed:', error);
        throw error;
      }
    }

    return lastProgress;
  }

  /**
   * Get current processing status
   */
  async getStatus(): Promise<ProcessingProgress> {
    try {
      const response = await fetch(`/api/upload/status?uploadId=${this.uploadId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to get status: ${response.statusText}`);
      }

      const status = await response.json();
      
      return {
        uploadId: this.uploadId,
        processedRows: status.progress.processedRows || 0,
        totalRows: status.progress.totalRows || 0,
        successfulRows: status.progress.successfulRows || 0,
        errorRows: status.progress.errorRows || 0,
        isComplete: status.status === 'completed',
        progress: status.progress.percentage || 0
      };

    } catch (error) {
      console.error('Failed to get processing status:', error);
      throw error;
    }
  }
}

/**
 * Utility function to start processing an upload
 */
export async function processUpload(
  uploadId: string,
  onProgress?: (progress: ProcessingProgress) => void
): Promise<ProcessingProgress> {
  const processor = new ClientCsvProcessor(uploadId, onProgress);
  return await processor.startProcessing();
}

/**
 * Utility function to monitor upload progress
 */
export async function monitorUpload(
  uploadId: string,
  onProgress: (progress: ProcessingProgress) => void,
  pollInterval: number = 2000
): Promise<ProcessingProgress> {
  const processor = new ClientCsvProcessor(uploadId);
  
  return new Promise((resolve, reject) => {
    const poll = async () => {
      try {
        const progress = await processor.getStatus();
        onProgress(progress);
        
        if (progress.isComplete) {
          resolve(progress);
        } else {
          setTimeout(poll, pollInterval);
        }
      } catch (error) {
        reject(error);
      }
    };
    
    poll();
  });
}
